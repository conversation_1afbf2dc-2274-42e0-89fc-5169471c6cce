const express = require('express');
const router = express.Router();
const { ensureAuthenticated } = require('../auth/googleAuth');
const prototypeService = require('../services/prototypeService');
const sessionService = require('../services/sessionService');

/**
 * POST /api/page_gen/project/list
 * List projects with pagination (matching Readdy.ai API structure)
 * Request body: { page: { pageNum: number, pageSize: number } }
 * Response: { projects: [], totalCount: number, page: { pageNum, pageSize } }
 */
router.post('/project/list', ensureAuthenticated, async (req, res) => {
  try {
    const { page } = req.body;
    const pageNum = page?.pageNum || 1;
    const pageSize = page?.pageSize || 50;

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Calculate offset for pagination
    const offset = (pageNum - 1) * pageSize;

    // Get paginated prototypes (projects)
    const prototypes = await prototypeService.getPrototypesByUserPaginated(userId, pageSize, offset);
    const totalCount = await prototypeService.getPrototypesCountByUser(userId);

    // Transform prototypes to match project structure
    const projects = prototypes.map(prototype => ({
      id: prototype.id,
      title: prototype.title,
      description: prototype.description,
      created_at: prototype.created_at,
      updated_at: prototype.updated_at,
      preview_image_url: prototype.preview_image_url,
      // Add any additional fields that match the reference API
      status: 'active', // Default status
      type: 'prototype' // Project type
    }));

    res.json({
      projects,
      totalCount,
      page: {
        pageNum,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    });
  } catch (error) {
    console.error('Error listing projects:', error);
    res.status(500).json({ error: 'Failed to list projects' });
  }
});

/**
 * POST /api/page_gen/project/create
 * Create a new project (matching Readdy.ai API structure)
 * Request body: { title: string, description?: string, template?: string }
 * Response: { success: boolean, project: object }
 */
router.post('/project/create', ensureAuthenticated, async (req, res) => {
  try {
    const { title, description, template } = req.body;

    if (!title || typeof title !== 'string') {
      return res.status(400).json({ error: 'Project title is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Create basic HTML template based on template type
    let html = '<div>New Project</div>';
    if (template === 'landing') {
      html = `
        <div class="container">
          <header>
            <h1>${title}</h1>
            <nav>
              <a href="#home">Home</a>
              <a href="#about">About</a>
              <a href="#contact">Contact</a>
            </nav>
          </header>
          <main>
            <section>
              <h2>Welcome</h2>
              <p>${description || 'Welcome to our landing page'}</p>
            </section>
          </main>
        </div>
      `;
    } else if (template === 'dashboard') {
      html = `
        <div class="dashboard">
          <aside class="sidebar">
            <h2>Dashboard</h2>
            <nav>
              <a href="#overview">Overview</a>
              <a href="#analytics">Analytics</a>
              <a href="#settings">Settings</a>
            </nav>
          </aside>
          <main class="content">
            <h1>${title}</h1>
            <p>${description || 'Dashboard content goes here'}</p>
          </main>
        </div>
      `;
    }

    // Create the project using existing prototype service
    const prototype = await prototypeService.createPrototype({
      user_id: userId,
      title: title.substring(0, 250), // Ensure title fits DB constraint
      description: description || '',
      html: html,
      css: '', // Default empty CSS
      preview_image_url: null,
      prompt_id: null
    });

    // Transform to project format
    const project = {
      id: prototype.id,
      title: prototype.title,
      description: prototype.description,
      created_at: prototype.created_at,
      updated_at: prototype.updated_at,
      preview_image_url: prototype.preview_image_url,
      status: 'active',
      type: 'prototype'
    };

    res.json({
      success: true,
      project
    });
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({ error: 'Failed to create project' });
  }
});

/**
 * POST /api/page_gen/session/list
 * List sessions (pages) for a project with pagination (matching Readdy.ai API structure)
 * Request body: { projectId: string, page: { pageNum: number, pageSize: number } }
 * Response: { sessions: [], totalCount: number, page: { pageNum, pageSize } }
 */
router.post('/session/list', ensureAuthenticated, async (req, res) => {
  try {
    const { projectId, page } = req.body;
    const pageNum = page?.pageNum || 1;
    const pageSize = page?.pageSize || 30;

    if (!projectId) {
      return res.status(400).json({ error: 'Project ID is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Verify that the project belongs to the user
    const project = await prototypeService.getPrototypeById(projectId, userId);
    if (!project) {
      return res.status(404).json({ error: 'Project not found or access denied' });
    }

    // Calculate offset for pagination
    const offset = (pageNum - 1) * pageSize;

    // Get paginated sessions for the project
    const sessions = await sessionService.getSessionsByProjectPaginated(projectId, userId, pageSize, offset);
    const totalCount = await sessionService.getSessionsCountByProject(projectId, userId);

    // Transform sessions to match the expected format
    const formattedSessions = sessions.map(session => ({
      id: session.id,
      projectId: session.prototype_id,
      pageUrl: session.page_url,
      sessionState: session.session_state,
      created_at: session.created_at,
      updated_at: session.updated_at,
      last_accessed: session.last_accessed,
      // Add additional fields that might be expected
      title: session.page_url.split('/').pop() || 'Untitled Page',
      type: 'page',
      status: session.session_state
    }));

    res.json({
      sessions: formattedSessions,
      totalCount,
      page: {
        pageNum,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    });
  } catch (error) {
    console.error('Error listing sessions:', error);
    res.status(500).json({ error: 'Failed to list sessions' });
  }
});

/**
 * POST /api/page_gen/project/update
 * Update an existing project
 * Request body: { projectId: string, title: string, description?: string }
 * Response: { success: boolean, project: object }
 */
router.post('/project/update', ensureAuthenticated, async (req, res) => {
  try {
    const { projectId, title, description } = req.body;

    if (!projectId) {
      return res.status(400).json({ error: 'Project ID is required' });
    }

    if (!title || typeof title !== 'string') {
      return res.status(400).json({ error: 'Project title is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Verify that the project belongs to the user
    const existingProject = await prototypeService.getPrototypeById(projectId, userId);
    if (!existingProject) {
      return res.status(404).json({ error: 'Project not found or access denied' });
    }

    // Update the project
    const updatedPrototype = await prototypeService.updatePrototype(projectId, userId, {
      title: title.substring(0, 250), // Ensure title fits DB constraint
      description: description || existingProject.description
    });

    // Transform to project format
    const project = {
      id: updatedPrototype.id,
      title: updatedPrototype.title,
      description: updatedPrototype.description,
      created_at: updatedPrototype.created_at,
      updated_at: updatedPrototype.updated_at,
      preview_image_url: updatedPrototype.preview_image_url,
      status: 'active',
      type: 'prototype'
    };

    res.json({
      success: true,
      project
    });
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(500).json({ error: 'Failed to update project' });
  }
});

/**
 * POST /api/page_gen/project/delete
 * Delete a project
 * Request body: { projectId: string }
 * Response: { success: boolean, message: string }
 */
router.post('/project/delete', ensureAuthenticated, async (req, res) => {
  try {
    const { projectId } = req.body;

    if (!projectId) {
      return res.status(400).json({ error: 'Project ID is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Verify that the project belongs to the user
    const existingProject = await prototypeService.getPrototypeById(projectId, userId);
    if (!existingProject) {
      return res.status(404).json({ error: 'Project not found or access denied' });
    }

    // Delete the project
    await prototypeService.deletePrototype(projectId, userId);

    res.json({
      success: true,
      message: 'Project deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({ error: 'Failed to delete project' });
  }
});

module.exports = router;
