// Project and page management API service
// Matches the backend API structure from pageGen.js

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

export interface Project {
  id: number;
  title: string;
  description?: string;
  created_at: string;
  updated_at: string;
  preview_image_url?: string;
  status: string;
  type: string;
}

export interface ProjectListResponse {
  projects: Project[];
  totalCount: number;
  page: {
    pageNum: number;
    pageSize: number;
    totalPages: number;
  };
}

export interface CreateProjectRequest {
  title: string;
  description?: string;
  template?: string;
}

export interface CreateProjectResponse {
  success: boolean;
  project: Project;
}

export interface UpdateProjectRequest {
  title: string;
  description?: string;
}

export interface UpdateProjectResponse {
  success: boolean;
  project: Project;
}

export interface DeleteProjectResponse {
  success: boolean;
  message: string;
}

export interface Page {
  id: number;
  project_id: number;
  title: string;
  url?: string;
  html?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface PageListResponse {
  sessions: Page[];
  totalCount: number;
  page: {
    pageNum: number;
    pageSize: number;
    totalPages: number;
  };
}

/**
 * Get paginated list of projects for the current user
 */
export async function getProjectList(pageNum: number = 1, pageSize: number = 12): Promise<ProjectListResponse> {
  const response = await fetch(`${API_BASE_URL}/page_gen/project/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      page: {
        pageNum,
        pageSize
      }
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch projects: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Create a new project
 */
export async function createProject(projectData: CreateProjectRequest): Promise<CreateProjectResponse> {
  const response = await fetch(`${API_BASE_URL}/page_gen/project/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(projectData)
  });

  if (!response.ok) {
    throw new Error(`Failed to create project: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Update a project
 */
export async function updateProject(projectId: number, projectData: UpdateProjectRequest): Promise<UpdateProjectResponse> {
  const response = await fetch(`${API_BASE_URL}/page_gen/project/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      projectId: projectId.toString(),
      ...projectData
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to update project: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Delete a project
 */
export async function deleteProject(projectId: number): Promise<DeleteProjectResponse> {
  const response = await fetch(`${API_BASE_URL}/page_gen/project/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      projectId: projectId.toString()
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to delete project: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get paginated list of pages/sessions for a project
 */
export async function getPageList(projectId: number, pageNum: number = 1, pageSize: number = 30): Promise<PageListResponse> {
  const response = await fetch(`${API_BASE_URL}/page_gen/session/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      projectId: projectId.toString(),
      page: {
        pageNum,
        pageSize
      }
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch pages: ${response.statusText}`);
  }

  return response.json();
}
